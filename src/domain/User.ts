import {z} from "zod";
import {v4} from "uuid";
import { ok, Result } from "../utils/Result";

const UserId = z.string().uuid().brand<"UserId">();

// ドメインオブジェクト
export const User = z.object({
  id: UserId,
  name: z.string().brand<"UserName">(),
  email: z.string().email(),
  password: z.string().brand<"HashedPassword">(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const CreateUser = User.omit({ id: true, createdAt: true, updatedAt: true });
export const UpdateUser = User.omit({ id: true, createdAt: true, updatedAt: true }).partial();
export const UserResponse = User.omit({ password: true, createdAt: true, updatedAt: true });

export type User = z.infer<typeof User>;
export type CreateUser = z.infer<typeof CreateUser>;
export type UpdateUser = z.infer<typeof UpdateUser>;
export type UserResponse = z.infer<typeof UserResponse>;



export const updateUser = (user: User, updateUser: UpdateUser): Result<User> => {
  return ok({
    ...user,
    ...updateUser,
    updatedAt: new Date(),
  });
};

export const createUser = (createUser: CreateUser): Result<User> => {
  return ok({
    id: UserId.parse(v4()),
    createdAt: new Date(),
    updatedAt: new Date(),
    ...createUser,
  });
};
